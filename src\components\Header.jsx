import React from "react";
import "./Header.css";
import logo from "../assets/logo.jpeg";
import search from "../assets/search.jpeg";
import { CgProfile } from "react-icons/cg";

const Header = () => {
  return (
    <div className="header">
      <div className="header-container">
        <div className="login">
          <button className="login-btn">
            <div className="profile-icon">
              <CgProfile size={18} />
            </div>

            <a>LOGIN | SIGNUP</a>
          </button>
        </div>
        <div className="logo">
          <img src={logo} alt="Logo" className="logo" />
        </div>

        <div className="search">
          <img src={search} alt="" />
        </div>
      </div>
    </div>
  );
};

export default Header;
